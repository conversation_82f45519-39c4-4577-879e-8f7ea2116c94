<?php

/**
 * Coin Detail Methods
 * Contains methods for retrieving detailed coin information
 */

function get_coin_detail_by_id($id)
{
    global $selectedLanguage;
    global $link;
    $id = intval($id);
    $query = "
    SELECT c.id, c.name, c.marketcap,c.marketcap_rank, c.geckoid, c.image, c.fdv, c.symbol, c.geckoslug,
    c2.price_change_1d, c2.price_change_7d, c2.price_change_30d, c2.price_change_90d, c2.price_change_1y,
    c2.links, c2.description
    FROM coindata c
    JOIN coindata2 c2 ON c.geckoid = c2.geckoid
    WHERE c.id = $id
    ";
    $result = mysqli_query($link, $query);
    $languageNameColumn = "name_" . strtoupper($selectedLanguage);
    $languageNameColumn = mysqli_real_escape_string($link, $languageNameColumn);
    if ($result && mysqli_num_rows($result) > 0) {
        $coin = mysqli_fetch_assoc($result);
        // Get the count of watchlists that contain this coin
        $watchlistCountQuery = "SELECT COUNT(DISTINCT watchlist_id) as watchlist_count
                               FROM watchlist_coins
                               WHERE coin_id = $id";
        $watchlistCountResult = mysqli_query($link, $watchlistCountQuery);
        $watchlistCount = 0;
        if ($watchlistCountResult && mysqli_num_rows($watchlistCountResult) > 0) {
            $watchlistCountRow = mysqli_fetch_assoc($watchlistCountResult);
            $watchlistCount = intval($watchlistCountRow['watchlist_count']);
        }
        $coin['watchlist_count'] = $watchlistCount;
        /* ---------------------------------- coindata_categories start --------------------------------- */
        $categoriesQuery = "select name from gecko_categories where name in ( SELECT category_name
                            FROM coindata_categories
                            WHERE geckoid = '" . mysqli_real_escape_string($link, $coin['geckoid']) . "' ) and isshown = 1";
        $categoriesResult = mysqli_query($link, $categoriesQuery);
        $categories = [];
        if ($categoriesResult && mysqli_num_rows($categoriesResult) > 0) {
            while ($category = mysqli_fetch_assoc($categoriesResult)) {
                $categories[] = $category['name'];
            }
        }
        $coin['categories'] = $categories;
        /* ----------------------------------- coindata_categories end ---------------------------------- */
        // Generate scores using the common method
        $coin['scores'] = generateCoinScores($coin['geckoid'], $selectedLanguage);
        $coin['marketData'] = [
            "Price" => 0,
            "MarketCap" => (float)$coin['marketcap'],
            "FullyDilutedValuation" => (float)$coin['fdv'],
            "Changes" => [
                ['name' => '24H', 'value' => (float)$coin['price_change_1d']],
                ['name' => '7D', 'value' => (float)$coin['price_change_7d']],
                ['name' => '30D', 'value' => (float)$coin['price_change_30d']],
                ["name" => '90D', 'value' => (float)$coin['price_change_90d']],
                ["name" => '1Y', 'value' => (float)$coin['price_change_1y']]
            ]
        ];
        $total_scoreQuery = "SELECT total_score FROM `coindata` where geckoid ='{$coin['geckoid']}'  ";
        $total_scoreResult = mysqli_query($link, $total_scoreQuery);
        if ($total_scoreResult && mysqli_num_rows($total_scoreResult) > 0) {
            $row = mysqli_fetch_assoc($total_scoreResult);
            $totalScore = isset($row['total_score']) ? (float)$row['total_score'] : null;
        } else {
            $totalScore = null;
        }
        $coin['total_score'] = $totalScore;

        // Parse links if available
        // Array to store social links and other links
        $socialLinks = [];
        $otherLinks = [];

        if (!empty($coin['links'])) {
            $linksData = json_decode($coin['links'], true);
            if (is_array($linksData)) {
                foreach ($linksData as $link) {
                    if (isset($link['type']) && isset($link['value'])) {
                        $linkItem = [
                            'type' => $link['type'],
                            'url' => $link['value']
                        ];

                        switch (strtolower($link['type'])) {
                            case 'web':
                            case 'website':
                            case 'whitepaper':
                            case 'github':
                            case 'twitter':
                            case 'telegram':
                            case 'discord':
                            case 'medium':
                            case 'facebook':
                            case 'youtube':
                            case 'linkedin':
                            case 'reddit':
                            case 'explorer':
                            case 'announcement':
                                $socialLinks[] = $linkItem;
                                break;
                            default:
                                $otherLinks[] = $linkItem;
                                break;
                        }
                    }
                }
            }
        }

        // Add social links to the response
        $coin['socialLinks'] = $socialLinks;
        $coin['otherLinks'] = $otherLinks;

        // Remove the original links field to avoid duplication
        unset($coin['links']);

        unset($coin['price_change_1d']);
        unset($coin['price_change_7d']);
        unset($coin['price_change_30d']);
        unset($coin['price_change_90d']);
        unset($coin['price_change_1y']);
        unset($coin['fdv']);
        $coin['gid'] = $coin['geckoid'];
        unset($coin['geckoid']);
        unset($coin['marketcap']);
        $response = new SuccessResult($coin);
        $response->send();
    } else {
        $response = new ErrorResult('Coin not found.');
        $response->send(404);
    }
}

function get_coin_summary($id)
{
    global $link;
    $id = intval($id);
    $query = "
        SELECT c.id, c.marketcap_rank, c.name, c.symbol, c.marketcap,
               c2.price_change_1d, c2.price_change_7d,
               c2.price_change_30d, c2.price_change_90d, c2.price_change_1y,
               c.geckoid, c.geckoslug
        FROM coindata c
        JOIN coindata2 c2 ON c.geckoid = c2.geckoid
        WHERE c.id = $id
    ";
    $result = mysqli_query($link, $query);
    if ($result && mysqli_num_rows($result) > 0) {
        $coin = mysqli_fetch_assoc($result);
        // Get the count of watchlists that contain this coin
        $watchlistCountQuery = "SELECT COUNT(DISTINCT watchlist_id) as watchlist_count
                               FROM watchlist_coins
                               WHERE coin_id = $id";
        $watchlistCountResult = mysqli_query($link, $watchlistCountQuery);
        $watchlistCount = 0;
        if ($watchlistCountResult && mysqli_num_rows($watchlistCountResult) > 0) {
            $watchlistCountRow = mysqli_fetch_assoc($watchlistCountResult);
            $watchlistCount = intval($watchlistCountRow['watchlist_count']);
        }
        $response = [
            "id" => (string)$coin['id'],
            "rank" => (int)$coin['marketcap_rank'],
            "name" => $coin['name'],
            "symbol" => $coin['symbol'],
            "marketcap" => (float)$coin['marketcap'],
            "price" => fetch_price_from_coingecko($coin['geckoslug']),
            "watchlist_count" => $watchlistCount,
            "priceChanges" => [
                "24h" => (float)$coin['price_change_1d'],
                "7d" => (float)$coin['price_change_7d'],
                "30d" => (float)$coin['price_change_30d'],
                "90d" => (float)$coin['price_change_90d'],
                "1y" => (float)$coin['price_change_1y'],
            ],
        ];
        $result = new SuccessResult($response);
        $result->send();
    } else {
        $error = new ErrorResult("Coin not found.");
        $error->send(404);
    }
}

function fetch_price_from_coingecko($coin_id)
{
    include_once('config.php');
    global $gecko_api_key;
    // Hata ayıklama için coin ID'sini yazdır
    error_log("fetch_price_from_coingecko için coin ID: " . $coin_id);
    $curl = curl_init();
    $url = 'https://pro-api.coingecko.com/api/v3/simple/price?ids=' . $coin_id . '&vs_currencies=usd&x_cg_pro_api_key=' . $gecko_api_key;
    error_log("CoinGecko API URL: " . $url);
    curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_HTTPHEADER => array(
            'Accept: application/json',
        ),
    ));
    $response = curl_exec($curl);
    $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    error_log("CoinGecko API yanıt kodu: " . $httpcode);
    error_log("CoinGecko API yanıtı: " . $response);
    if ($httpcode != 200) {
        error_log("CoinGecko API hatası: gecko_id=" . $coin_id);
        error_log("Response = " . $response);
        return 0; // Hata durumunda 0 döndür
    }
    $gecko_coin_data = json_decode($response, true);
    $price = $gecko_coin_data[$coin_id]['usd'] ?? 0;
    error_log("Bulunan fiyat: " . $price);
    return $price;
}

function get_adv_coin_detail_by_id($id)
{
    global $selectedLanguage;
    global $link;
    $id = intval($id);
    $query = "
    SELECT c.id,
    c.name,
    c.cr_id,
    c.marketcap,
    c.geckoid,
    c.image,
    c.fdv,
    c.symbol,
    c.geckoslug,
    c2.price_change_1d,
    c2.price_change_7d,
    c2.price_change_30d,
    c2.price_change_90d,
    c2.price_change_1y,
    c2.links,
    c2.description,
    c2.ico_roi,
    c2.ico_price,
    c2.nextunlock,
    c2.unlock_amount,
    c.max_supply,
    c.total_supply,
    c.circulating_supply,
    c.total_volume,
    c.marketcap_rank,
    c.sparkline7d,
    c.ath,
    c.atl,
    c.cr_id,
    c.ath_change,
    c.atl_change
    FROM coindata c
    JOIN coindata2 c2 ON c.geckoid = c2.geckoid
    WHERE c.id = $id
    ";
    $result = mysqli_query($link, $query);
    $languageNameColumn = "name_" . strtoupper($selectedLanguage);
    $languageNameColumn = mysqli_real_escape_string($link, $languageNameColumn);
    if ($result && mysqli_num_rows($result) > 0) {
        $coin = mysqli_fetch_assoc($result);
        // Get sparkline data from cr_sparkline table
        $sparklineQuery = "SELECT spark_timestamp, spark_value
                          FROM cr_sparkline
                          WHERE cr_key = '{$coin['cr_id']}'
                          ORDER BY spark_timestamp ASC";
        $sparklineResult = mysqli_query($link, $sparklineQuery);
        $sparklineData = [];
        if ($sparklineResult && mysqli_num_rows($sparklineResult) > 0) {
            while ($sparkRow = mysqli_fetch_assoc($sparklineResult)) {
                $sparklineData[] = [
                    'timestamp' => $sparkRow['spark_timestamp'],
                    'value' => (float)$sparkRow['spark_value']
                ];
            }
        }
        $coin['sparkline'] = $sparklineData;
        // Get the count of watchlists that contain this coin
        $watchlistCountQuery = "SELECT COUNT(DISTINCT watchlist_id) as watchlist_count
                               FROM watchlist_coins
                               WHERE coin_id = $id";
        $watchlistCountResult = mysqli_query($link, $watchlistCountQuery);
        $watchlistCount = 0;
        if ($watchlistCountResult && mysqli_num_rows($watchlistCountResult) > 0) {
            $watchlistCountRow = mysqli_fetch_assoc($watchlistCountResult);
            $watchlistCount = intval($watchlistCountRow['watchlist_count']);
        }
        $coin['watchlist_count'] = $watchlistCount;
        /* ---------------------------------- coindata_categories start --------------------------------- */
        $categoriesQuery = "select name from gecko_categories where name in ( SELECT category_name
                            FROM coindata_categories
                            WHERE geckoid = '" . mysqli_real_escape_string($link, $coin['geckoid']) . "' ) and isshown = 1";
        $categoriesResult = mysqli_query($link, $categoriesQuery);
        $categories = [];
        if ($categoriesResult && mysqli_num_rows($categoriesResult) > 0) {
            while ($category = mysqli_fetch_assoc($categoriesResult)) {
                $categories[] = $category['name'];
            }
        }
        $coin['categories'] = $categories;
        /* ----------------------------------- coindata_categories end ---------------------------------- */
        // Generate scores using the common method
        $coin['scores'] = generateCoinScores($coin['geckoid'], $selectedLanguage);
        $coin['marketData'] = [
            "Price" => 0,
            "H24Volume" => (float)$coin['total_volume'],
            "MarketCap" => (float)$coin['marketcap'],
            "FullyDilutedValuation" => (float)$coin['fdv'],
            "Changes" => [
                ['name' => '24H', 'value' => (float)$coin['price_change_1d']],
                ['name' => '7D', 'value' => (float)$coin['price_change_7d']],
                ['name' => '30D', 'value' => (float)$coin['price_change_30d']],
                ["name" => '90D', 'value' => (float)$coin['price_change_90d']],
                ["name" => '1Y', 'value' => (float)$coin['price_change_1y']]
            ]
        ];
        $total_scoreQuery = "SELECT total_score FROM `coindata` where geckoid ='{$coin['geckoid']}'  ";
        $total_scoreResult = mysqli_query($link, $total_scoreQuery);
        if ($total_scoreResult && mysqli_num_rows($total_scoreResult) > 0) {
            $row = mysqli_fetch_assoc($total_scoreResult);
            $totalScore = isset($row['total_score']) ? (float)$row['total_score'] : null;
        } else {
            $totalScore = null;
        }
        $coin['total_score'] = $totalScore;

        // Get total score history using cr_id
        $scoreHistoryQuery = "SELECT score, score_date
                     FROM total_score_history
                     WHERE cr_id = '{$coin['cr_id']}'
                     AND score_date >= NOW() - INTERVAL 180 DAY
                     ORDER BY score_date ASC";
        $scoreHistoryResult = mysqli_query($link, $scoreHistoryQuery);
        $totalScoreHistory = [];
        if ($scoreHistoryResult && mysqli_num_rows($scoreHistoryResult) > 0) {
            while ($historyRow = mysqli_fetch_assoc($scoreHistoryResult)) {
                $totalScoreHistory[] = [
                    'date' => $historyRow['score_date'],
                    'score' => (int)$historyRow['score']
                ];
            }
        }
        $coin['total_score_history'] = $totalScoreHistory;

        // Parse links if available
        // Array to store social links and other links
        $socialLinks = [];
        $otherLinks = [];

        if (!empty($coin['links'])) {
            $linksData = json_decode($coin['links'], true);
            if (is_array($linksData)) {
                foreach ($linksData as $link) {
                    if (isset($link['type']) && isset($link['value'])) {
                        $linkItem = [
                            'type' => $link['type'],
                            'url' => $link['value']
                        ];

                        switch (strtolower($link['type'])) {
                            case 'web':
                            case 'website':
                            case 'whitepaper':
                            case 'github':
                            case 'twitter':
                            case 'telegram':
                            case 'discord':
                            case 'medium':
                            case 'facebook':
                            case 'youtube':
                            case 'linkedin':
                            case 'reddit':
                            case 'explorer':
                            case 'announcement':
                                $socialLinks[] = $linkItem;
                                break;
                            default:
                                $otherLinks[] = $linkItem;
                                break;
                        }
                    }
                }
            }
        }

        // Add social links to the response
        $coin['socialLinks'] = $socialLinks;
        $coin['otherLinks'] = $otherLinks;

        // Remove the original links field to avoid duplication
        unset($coin['links']);

        unset($coin['price_change_1d']);
        unset($coin['price_change_7d']);
        unset($coin['price_change_30d']);
        unset($coin['price_change_90d']);
        unset($coin['price_change_1y']);
        unset($coin['total_volume']);
        unset($coin['fdv']);
        unset($coin['cr_id']);
        $coin['gid'] = $coin['geckoid'];
        unset($coin['geckoid']);
        unset($coin['marketcap']);
        $response = new SuccessResult($coin);
        $response->send();
    } else {
        $response = new ErrorResult('Coin not found.');
        $response->send(404);
    }
}

/**
 * Generate scores array for a coin based on metric groups
 * @param string $geckoid The coin's gecko ID
 * @param string $selectedLanguage The selected language for metric names
 * @return array The scores array with metric groups and sub-scores
 */
function generateCoinScores($geckoid, $selectedLanguage)
{
    global $link;

    $languageNameColumn = "name_" . strtoupper($selectedLanguage);
    $languageNameColumn = mysqli_real_escape_string($link, $languageNameColumn);

    $metricGroupsQuery = "SELECT id, name, value FROM metric_groups WHERE name != 'Vesting'";
    $metricGroupsResult = mysqli_query($link, $metricGroupsQuery);
    $scores = [];
    $totalMetricValue = 0;
    $metricGroups = [];

    // First, get all metric groups and calculate total value
    if ($metricGroupsResult && mysqli_num_rows($metricGroupsResult) > 0) {
        while ($group = mysqli_fetch_assoc($metricGroupsResult)) {
            $metricGroups[] = $group;
            $totalMetricValue += floatval($group['value']);
        }
    }

    // Now process each metric group with its calculated weight
    foreach ($metricGroups as $group) {
        $groupId = $group['id'];
        $groupName = getMetricGroupNameById($groupId, $selectedLanguage);
        $groupValue = floatval($group['value']);
        $groupWeight = ($totalMetricValue > 0) ? ($groupValue / $totalMetricValue) * 100 : 0;

        $metricSubgroupsQuery = "
        SELECT
        mt.`$languageNameColumn` as name,
        m.id,
        COALESCE(cm.score, c.score) AS final_score
        FROM metric_subgroups m
        JOIN coin_scores c ON m.id = c.metric_subgroup
        LEFT JOIN coin_scores_manual cm ON c.geckoid = cm.geckoid AND cm.metric_subgroup = m.id
        JOIN metric_translations mt on m.id = mt.metric_id
        WHERE m.metric_group = $groupId AND c.geckoid = '{$geckoid}' AND m.isactive = 1
        ";

        $metricSubgroupsResult = mysqli_query($link, $metricSubgroupsQuery);
        $subScores = [];
        if ($metricSubgroupsResult && mysqli_num_rows($metricSubgroupsResult) > 0) {
            while ($sub = mysqli_fetch_assoc($metricSubgroupsResult)) {
                $subScores[] = [
                    'id' => $sub['id'],
                    'name' => $sub['name'],
                    'value' => isset($sub['final_score']) ? (float)$sub['final_score'] : null
                ];
            }
        }

        $totalQuery = "SELECT score FROM `coin_group_scores_all` where metric_group = $groupId and geckoid ='{$geckoid}'  ";
        $totalResult = mysqli_query($link, $totalQuery);
        if ($totalResult && mysqli_num_rows($totalResult) > 0) {
            $row = mysqli_fetch_assoc($totalResult);
            $totalScore = isset($row['score']) ? (float)$row['score'] : null;
        } else {
            $totalScore = null;
        }

        $scores[] = [
            'name' => $groupName,
            'total' => $totalScore,
            'weight' => round($groupWeight, 2),
            'subScores' => $subScores
        ];
    }

    return $scores;
}
