<?php


class SuccessResult
{
    public $success;
    public $output;
    public function __construct($data)
    {
        $this->success = true;
        $this->output = $data;
    }
    public function send($code = 200)
    {
        http_response_code($code);
        echo json_encode($this, JSON_PRETTY_PRINT);
    }
}
class ErrorResult
{
    public $success;
    public $errormsg;
    public function __construct($errormsg)
    {
        $this->success = false;
        $this->errormsg = $errormsg;
    }
    public function send($code = 500)
    {
        http_response_code($code);
        echo json_encode($this, JSON_PRETTY_PRINT);
    }
}

/**
 * Registration method constants
 */
class RegistrationMethod
{
    const GOOGLE = 'google';
    const EMAIL = 'email';
    const TWITTER = 'twitter';
    const APPLE = 'apple';
}

?>